# 3D文创图案贴图工具

这是一个交互式的3D文创图案贴图工具，可以将平面的文创图案贴到3D杯子模型上，支持实时预览和交互式调整。

## 功能特点

- 🎨 **智能贴图**: 将平面文创图案自动贴合到3D杯身表面
- 🖱️ **交互式操作**: 通过鼠标拖拽和滑块调整贴图位置、大小、旋转
- 👀 **实时预览**: 实时查看贴图效果，所见即所得
- 📐 **精确控制**: 支持UV坐标精确定位和参数微调
- 💾 **模型导出**: 支持导出带贴图的3D模型文件
- 🏺 **自动生成**: 内置杯子模型生成器，自带UV映射

## 系统要求

- Python 3.8+
- Windows/macOS/Linux
- 至少4GB内存
- 支持OpenGL的显卡

## 安装步骤

1. **克隆或下载项目文件**
   ```bash
   # 确保所有文件都在同一目录下
   demo.py
   texture_mapper.py
   cup_generator.py
   requirements.txt
   pattern.png (示例图案)
   ```

2. **安装依赖包**
   ```bash
   pip install -r requirements.txt
   ```

3. **运行程序**
   ```bash
   python demo.py
   ```

## 使用流程

### 1. 启动程序
运行 `python demo.py`，程序会自动生成一个带UV映射的杯子模型。

### 2. 加载文创图案
- 点击"加载图案"按钮
- 选择您的文创图案文件（支持PNG、JPG、JPEG、BMP、TIFF格式）
- 图案会显示在左侧预览区域

### 3. 启动3D视图
- 点击"启动3D视图"按钮
- 会打开一个新的3D预览窗口，显示杯子模型

### 4. 调整贴图参数
使用左侧控制面板的滑块调整：
- **位置 U**: 水平方向位置（0-1）
- **位置 V**: 垂直方向位置（0-1）
- **缩放**: 图案大小（0.1-1.0）
- **旋转**: 图案旋转角度（0-360度）

### 5. 实时预览
- 调整参数时，3D视图会实时更新
- 点击"预览效果"按钮强制刷新预览

### 6. 确认和导出
- 满意效果后，点击"确认贴图"
- 点击"导出模型"保存最终的3D模型

## 操作技巧

### 3D视图操作
- **鼠标左键拖拽**: 旋转视角
- **鼠标滚轮**: 缩放视图
- **鼠标中键拖拽**: 平移视图

### 贴图调整技巧
1. **粗调**: 使用滑块快速调整大致位置
2. **精调**: 微调滑块获得精确位置
3. **重置**: 点击"重置位置"回到初始状态
4. **预览**: 随时点击"预览效果"查看最新效果

### 最佳实践
- 建议图案分辨率不超过1024x1024，以保证性能
- 使用PNG格式的图案可以支持透明背景
- 调整缩放时，0.2-0.5通常效果较好
- 可以多次调整参数，找到最佳贴图位置

## 文件说明

- `demo.py`: 主程序入口
- `texture_mapper.py`: 贴图应用程序主体
- `cup_generator.py`: 杯子3D模型生成器
- `requirements.txt`: Python依赖包列表
- `pattern.png`: 示例文创图案

## 技术架构

- **3D渲染**: Open3D
- **GUI界面**: tkinter
- **图像处理**: PIL, OpenCV
- **3D建模**: numpy, trimesh
- **UV映射**: 自定义算法

## 故障排除

### 常见问题

1. **依赖包安装失败**
   ```bash
   # 尝试升级pip
   python -m pip install --upgrade pip
   # 重新安装依赖
   pip install -r requirements.txt
   ```

2. **3D视图无法启动**
   - 检查显卡驱动是否支持OpenGL
   - 尝试更新显卡驱动

3. **图案加载失败**
   - 确认图片格式是否支持
   - 检查图片文件是否损坏

4. **贴图效果不理想**
   - 调整图案分辨率
   - 尝试不同的缩放和位置参数

### 性能优化

- 使用较小的图案文件（建议<2MB）
- 关闭其他占用GPU的程序
- 降低3D视图窗口大小

## 扩展功能

该工具支持以下扩展：
- 添加更多3D模型（茶壶、花瓶等）
- 支持多个图案同时贴图
- 添加材质和光照效果
- 支持更多导出格式

## 技术支持

如遇到问题，请检查：
1. Python版本是否为3.8+
2. 所有依赖包是否正确安装
3. 图案文件格式是否支持
4. 系统是否支持OpenGL

## 快速开始

### 验证安装
运行快速测试确保所有组件正常工作：
```bash
python quick_test.py
```

如果看到"🎉 所有测试通过！"，说明安装成功。

### 启动程序
```bash
python demo.py
```

## 项目文件说明

生成的文件：
- `cup_with_uv.obj` - 带UV映射的杯子模型
- `test_texture.png` - 测试用的红色圆形纹理
- `quick_test_cup.obj` - 快速测试生成的杯子模型

## 示例操作流程

1. **启动程序**: `python demo.py`
2. **加载图案**: 点击"加载图案"，选择`pattern.png`或您的图案文件
3. **启动3D视图**: 点击"启动3D视图"按钮
4. **调整参数**: 使用滑块调整位置、缩放、旋转
5. **预览效果**: 点击"预览效果"查看贴图
6. **确认贴图**: 满意后点击"确认贴图"
7. **导出模型**: 点击"导出模型"保存最终结果

---

**注意**: 首次运行时，程序会自动生成杯子模型，可能需要几秒钟时间。
