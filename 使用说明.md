# 3D文创图案贴图工具 - 最终版本

## 简介
这是一个高质量的3D杯子贴图工具，支持实时预览和交互式调整。

## 主要特性
- **高分辨率杯子模型**：使用120-150个分辨率点生成精细网格
- **实时贴图预览**：支持位置、大小、旋转的实时调整
- **交互式3D视图**：鼠标拖拽旋转视角
- **高质量渲染**：支持高质量和超高质量两种模式

## 使用方法

### 1. 启动程序
```bash
python demo.py
```

### 2. 加载图案
- 点击"加载图案"按钮
- 选择PNG、JPG等格式的图像文件
- 程序会自动应用贴图到杯子模型

### 3. 调整贴图参数
- **图片中心 - 水平位置**：调整图片中心在杯子上的水平位置
- **图片中心 - 垂直位置**：调整图片中心在杯子上的垂直位置
- **图片大小**：缩放图片的大小（0.05-1.0）
- **图片旋转角度**：以图片中心为轴旋转图片（0-360度）

### 贴图特性
- **单张贴图**：只将一张图片贴到指定位置，不重复铺满
- **精确定位**：通过中心点坐标精确控制图片位置
- **中心旋转**：旋转以图片中心为轴，不会偏移位置
- **透明背景**：图片范围外保持杯子原色（白色）

### 4. 3D视图操作
- **鼠标拖拽**：旋转3D视角
- **重置视角**：恢复默认视角
- **重置位置**：恢复贴图参数到默认值

### 5. 质量设置
- **高质量**：100个分辨率点，适合快速预览
- **超高质量**：150个分辨率点，适合最终效果

## 技术特点

### 高分辨率网格
- 杯子模型使用120-150个分辨率点
- 生成7000+个顶点和14000+个面片
- 确保贴图效果平滑细腻

### 优化的UV映射
- 精确的UV坐标计算
- 支持图片的精确定位、缩放、旋转
- 单张图片贴图，不重复铺满
- 以图片中心为旋转轴的旋转算法

### 实时渲染
- 基于matplotlib的3D渲染
- 顶点颜色插值
- 平滑的光照效果

## 依赖包
```
numpy
matplotlib
PIL (Pillow)
tkinter (Python内置)
```

## 注意事项
- 建议使用PNG格式的图案文件以获得最佳效果
- 超高质量模式可能在低配置电脑上运行较慢
- 支持的图像格式：PNG、JPG、JPEG、BMP、GIF、TIFF

## 故障排除
如果遇到中文字体显示问题，程序仍可正常使用，只是标题可能显示为方框。
