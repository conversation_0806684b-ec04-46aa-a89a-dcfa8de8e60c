# 3D文创图案贴图工具 - 专业版

## 简介
这是一个专业级的3D杯子贴图工具，具有美观的界面设计和高质量的渲染效果，支持实时预览和交互式调整。

## 主要特性
- **🎨 美观界面**：现代化的用户界面设计，分区清晰，操作直观
- **🏆 高质量杯子模型**：真实的杯子形状，包含杯身、杯底和把手
- **🖼️ 单张图片贴图**：精确控制图片位置，不重复铺满
- **🔄 实时预览**：参数调整时立即显示效果
- **🎯 双线性插值**：提升图片渲染质量，减少锯齿
- **👁️ 无坐标轴显示**：纯净的3D视图，专注于产品展示
- **🖱️ 交互式3D视图**：鼠标拖拽旋转视角

## 使用方法

### 1. 启动程序
```bash
python demo.py
```

### 2. 加载图案
- 点击"加载图案"按钮
- 选择PNG、JPG等格式的图像文件
- 程序会自动应用贴图到杯子模型

### 3. 调整贴图参数
- **图片中心 - 水平位置**：调整图片中心在杯子上的水平位置
- **图片中心 - 垂直位置**：调整图片中心在杯子上的垂直位置
- **图片大小**：缩放图片的大小（0.05-1.0）
- **图片旋转角度**：以图片中心为轴旋转图片（0-360度）

### 贴图特性
- **单张贴图**：只将一张图片贴到指定位置，不重复铺满
- **精确定位**：通过中心点坐标精确控制图片位置
- **中心旋转**：旋转以图片中心为轴，不会偏移位置
- **透明背景**：图片范围外保持杯子原色（白色）

### 4. 3D视图操作
- **鼠标拖拽**：旋转3D视角
- **重置视角**：恢复默认视角
- **重置位置**：恢复贴图参数到默认值

### 5. 质量设置
- **高质量**：100个分辨率点，适合快速预览
- **超高质量**：150个分辨率点，适合最终效果

## 技术特点

### 🏗️ 优化的杯子模型
- 真实的杯子轮廓，底部收缩，顶部外扩
- 包含杯身、杯底和把手的完整模型
- 平衡质量和性能的80个分辨率点
- 约3000+个顶点，6000+个面片

### 🎨 高质量贴图算法
- 双线性插值技术，提升图片清晰度
- 精确的UV坐标计算
- 支持图片的精确定位、缩放、旋转
- 单张图片贴图，不重复铺满
- 以图片中心为旋转轴的旋转算法

### 🖥️ 优化的渲染效果
- 移除坐标轴和网格线，纯净视觉
- 浅灰色背景，突出产品效果
- 无边线渲染，更加美观
- 使用draw_idle提升性能

### 💻 现代化界面设计
- 分区布局：文件加载、位置控制、变换控制、操作选项
- 图标和表情符号增强视觉效果
- 实时状态反馈和参数显示
- 响应式布局，支持窗口缩放

## 依赖包
```
numpy
matplotlib
PIL (Pillow)
tkinter (Python内置)
```

## 注意事项
- 建议使用PNG格式的图案文件以获得最佳效果
- 超高质量模式可能在低配置电脑上运行较慢
- 支持的图像格式：PNG、JPG、JPEG、BMP、GIF、TIFF

## 故障排除
如果遇到中文字体显示问题，程序仍可正常使用，只是标题可能显示为方框。
