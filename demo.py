"""
3D文创图案贴图Demo - 最终版本
高质量杯子模型生成和贴图功能的完整程序
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import numpy as np
from PIL import Image, ImageTk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
from mpl_toolkits.mplot3d.art3d import Poly3DCollection

# 设置matplotlib支持中文
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False


class HighQualityCupGenerator:
    """高质量杯子模型生成器"""

    def __init__(self):
        self.cup_height = 10.0
        self.cup_radius_bottom = 2.8
        self.cup_radius_top = 3.2
        self.cup_thickness = 0.15
        self.handle_enabled = True

    def generate_high_res_cup(self, resolution=80):
        """生成高分辨率杯子模型（优化性能）"""
        # 平衡质量和性能：使用80个分辨率点
        theta = np.linspace(0, 2*np.pi, resolution)
        heights = np.linspace(0, self.cup_height, resolution//2)

        vertices = []
        faces = []
        uv_coords = []

        # 生成杯身外表面（更真实的杯子形状）
        for i, h in enumerate(heights):
            # 使用更自然的杯子轮廓
            t_norm = h / self.cup_height
            # 杯子底部稍微收缩，顶部略微外扩
            radius_factor = 1.0 + 0.1 * np.sin(t_norm * np.pi * 0.8)
            r = self.cup_radius_bottom + (self.cup_radius_top - self.cup_radius_bottom) * t_norm * radius_factor

            for j, t in enumerate(theta[:-1]):
                x = r * np.cos(t)
                y = r * np.sin(t)
                z = h
                vertices.append([x, y, z])

                # 高精度UV坐标映射
                u = t / (2 * np.pi)
                v = h / self.cup_height
                uv_coords.append([u, v])

        # 添加杯底
        bottom_vertices_start = len(vertices)
        for j, t in enumerate(theta[:-1]):
            x = self.cup_radius_bottom * 0.95 * np.cos(t)  # 底部稍小
            y = self.cup_radius_bottom * 0.95 * np.sin(t)
            z = 0
            vertices.append([x, y, z])

            # 底部UV坐标
            u = (np.cos(t) + 1) * 0.5
            v = (np.sin(t) + 1) * 0.5
            uv_coords.append([u, v])

        # 添加杯底中心点
        vertices.append([0, 0, 0])
        uv_coords.append([0.5, 0.5])
        center_idx = len(vertices) - 1

        # 生成杯身面片
        n_theta = len(theta) - 1
        n_height = len(heights)

        for i in range(n_height - 1):
            for j in range(n_theta):
                v1 = i * n_theta + j
                v2 = i * n_theta + (j + 1) % n_theta
                v3 = (i + 1) * n_theta + j
                v4 = (i + 1) * n_theta + (j + 1) % n_theta

                faces.append([v1, v2, v4])
                faces.append([v1, v4, v3])

        # 生成杯底面片
        for j in range(n_theta):
            v1 = bottom_vertices_start + j
            v2 = bottom_vertices_start + (j + 1) % n_theta
            faces.append([center_idx, v1, v2])

        # 添加简单把手
        if self.handle_enabled:
            vertices, faces, uv_coords = self.add_simple_handle(vertices, faces, uv_coords)

        return np.array(vertices), np.array(faces), np.array(uv_coords)

    def add_simple_handle(self, vertices, faces, uv_coords):
        """添加简化的把手"""
        handle_vertices = []
        handle_faces = []
        handle_uv = []

        # 把手参数
        handle_segments = 12
        handle_radius = 0.12

        # 把手路径（半圆弧）
        angles = np.linspace(0.2, np.pi - 0.2, handle_segments)
        handle_center_x = self.cup_radius_top + 0.8
        handle_start_z = self.cup_height * 0.25
        handle_end_z = self.cup_height * 0.75

        start_vertex_idx = len(vertices)

        # 生成把手骨架
        for i, angle in enumerate(angles):
            x = handle_center_x + 0.6 * np.cos(angle)
            y = 0
            z = handle_start_z + (handle_end_z - handle_start_z) * i / (handle_segments - 1)

            # 为每个点生成小圆截面
            for j in range(6):  # 6边形截面
                section_angle = j * 2 * np.pi / 6
                dx = handle_radius * np.cos(section_angle)
                dy = handle_radius * np.sin(section_angle)

                handle_vertices.append([x + dx, y + dy, z])
                handle_uv.append([0.9, 0.9])  # 把手使用固定UV

        # 生成把手面片
        for i in range(handle_segments - 1):
            for j in range(6):
                v1 = start_vertex_idx + i * 6 + j
                v2 = start_vertex_idx + i * 6 + (j + 1) % 6
                v3 = start_vertex_idx + (i + 1) * 6 + j
                v4 = start_vertex_idx + (i + 1) * 6 + (j + 1) % 6

                handle_faces.append([v1, v2, v4])
                handle_faces.append([v1, v4, v3])

        # 合并数据
        all_vertices = np.vstack([vertices, handle_vertices])
        all_faces = np.vstack([faces, handle_faces])
        all_uv = np.vstack([uv_coords, handle_uv])

        return all_vertices, all_faces, all_uv


class FinalTextureMapper:
    """最终版本贴图器 - 整合所有优化功能"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("3D文创图案贴图工具 - 专业版")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f0f0f0')

        # 设置窗口图标和样式
        self.root.resizable(True, True)
        self.root.minsize(1200, 800)

        # 配置ttk样式
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('Title.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Heading.TLabel', font=('Arial', 10, 'bold'))

        # 数据存储
        self.pattern_image = None
        self.cup_vertices = None
        self.cup_faces = None
        self.cup_uv = None
        self.vertex_colors = None

        # 贴图参数
        self.current_texture_pos = [0.0, 0.0]
        self.texture_scale = 0.5
        self.texture_rotation = 0

        # 创建界面
        self.setup_ui()

        # 生成高质量杯子模型
        self.generate_cup_model()

        # 初始化3D显示
        self.setup_3d_display()

    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # 左侧控制面板
        control_frame = ttk.LabelFrame(main_frame, text="🎨 控制面板", padding=15)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 15))
        control_frame.configure(width=300)

        # 图案加载区域
        file_frame = ttk.LabelFrame(control_frame, text="📁 图案文件", padding=10)
        file_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Button(file_frame, text="🖼️ 加载图案文件",
                  command=self.load_pattern,
                  style='Accent.TButton').pack(fill=tk.X, pady=5)

        # 当前文件显示
        self.file_label = ttk.Label(file_frame, text="未选择文件",
                                   foreground='gray', font=('Arial', 9))
        self.file_label.pack(anchor=tk.W, pady=2)

        # 位置控制区域
        pos_frame = ttk.LabelFrame(control_frame, text="📍 图片位置", padding=10)
        pos_frame.pack(fill=tk.X, pady=(0, 15))

        # U坐标
        ttk.Label(pos_frame, text="水平位置:", style='Heading.TLabel').pack(anchor=tk.W)
        self.u_var = tk.DoubleVar(value=0.5)
        u_frame = ttk.Frame(pos_frame)
        u_frame.pack(fill=tk.X, pady=2)
        ttk.Label(u_frame, text="左", font=('Arial', 8)).pack(side=tk.LEFT)
        self.u_scale = ttk.Scale(u_frame, from_=0.0, to=1.0,
                                variable=self.u_var, orient=tk.HORIZONTAL,
                                command=self.on_texture_change)
        self.u_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        ttk.Label(u_frame, text="右", font=('Arial', 8)).pack(side=tk.RIGHT)

        # V坐标
        ttk.Label(pos_frame, text="垂直位置:", style='Heading.TLabel').pack(anchor=tk.W, pady=(10, 0))
        self.v_var = tk.DoubleVar(value=0.5)
        v_frame = ttk.Frame(pos_frame)
        v_frame.pack(fill=tk.X, pady=2)
        ttk.Label(v_frame, text="下", font=('Arial', 8)).pack(side=tk.LEFT)
        self.v_scale = ttk.Scale(v_frame, from_=0.0, to=1.0,
                                variable=self.v_var, orient=tk.HORIZONTAL,
                                command=self.on_texture_change)
        self.v_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        ttk.Label(v_frame, text="上", font=('Arial', 8)).pack(side=tk.RIGHT)

        # 大小和旋转控制区域
        transform_frame = ttk.LabelFrame(control_frame, text="🔧 变换控制", padding=10)
        transform_frame.pack(fill=tk.X, pady=(0, 15))

        # 缩放控制
        ttk.Label(transform_frame, text="图片大小:", style='Heading.TLabel').pack(anchor=tk.W)
        self.scale_var = tk.DoubleVar(value=0.3)
        scale_frame = ttk.Frame(transform_frame)
        scale_frame.pack(fill=tk.X, pady=2)
        ttk.Label(scale_frame, text="小", font=('Arial', 8)).pack(side=tk.LEFT)
        self.scale_scale = ttk.Scale(scale_frame, from_=0.05, to=1.0,
                                    variable=self.scale_var, orient=tk.HORIZONTAL,
                                    command=self.on_texture_change)
        self.scale_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        ttk.Label(scale_frame, text="大", font=('Arial', 8)).pack(side=tk.RIGHT)

        # 旋转控制
        ttk.Label(transform_frame, text="旋转角度:", style='Heading.TLabel').pack(anchor=tk.W, pady=(10, 0))
        self.rot_var = tk.DoubleVar(value=0)
        rot_frame = ttk.Frame(transform_frame)
        rot_frame.pack(fill=tk.X, pady=2)
        ttk.Label(rot_frame, text="0°", font=('Arial', 8)).pack(side=tk.LEFT)
        self.rot_scale = ttk.Scale(rot_frame, from_=0, to=360,
                                  variable=self.rot_var, orient=tk.HORIZONTAL,
                                  command=self.on_texture_change)
        self.rot_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        ttk.Label(rot_frame, text="360°", font=('Arial', 8)).pack(side=tk.RIGHT)

        # 质量和操作区域
        action_frame = ttk.LabelFrame(control_frame, text="⚙️ 操作选项", padding=10)
        action_frame.pack(fill=tk.X, pady=(0, 15))

        # 质量选项
        ttk.Label(action_frame, text="渲染质量:", style='Heading.TLabel').pack(anchor=tk.W)
        self.quality_var = tk.StringVar(value="高质量")
        quality_frame = ttk.Frame(action_frame)
        quality_frame.pack(fill=tk.X, pady=5)
        ttk.Radiobutton(quality_frame, text="高质量", variable=self.quality_var,
                       value="高质量").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(quality_frame, text="超高质量", variable=self.quality_var,
                       value="超高质量").pack(side=tk.LEFT)

        # 操作按钮
        button_frame = ttk.Frame(action_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(button_frame, text="🔄 重置位置",
                  command=self.reset_texture).pack(fill=tk.X, pady=2)
        ttk.Button(button_frame, text="👁️ 重置视角",
                  command=self.reset_view).pack(fill=tk.X, pady=2)

        # 右侧3D视图区域
        view_frame = ttk.LabelFrame(main_frame, text="🎯 3D实时预览", padding=10)
        view_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 添加提示标签
        tip_label = ttk.Label(view_frame,
                             text="💡 提示：拖拽鼠标旋转视角，加载图案后可实时调整参数",
                             font=('Arial', 9), foreground='#666666')
        tip_label.pack(anchor=tk.W, pady=(0, 10))

        # 状态栏
        status_frame = ttk.Frame(self.root)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=5)

        self.status_var = tk.StringVar(value="🟢 就绪 - 请加载图案文件开始使用")
        status_bar = ttk.Label(status_frame, textvariable=self.status_var,
                              font=('Arial', 9), foreground='#333333')
        status_bar.pack(side=tk.LEFT)

        return view_frame

    def generate_cup_model(self):
        """生成高质量杯子模型"""
        self.status_var.set("正在生成高质量杯子模型...")
        self.root.update()

        # 使用高分辨率生成器
        generator = HighQualityCupGenerator()

        # 根据质量设置选择分辨率
        if self.quality_var.get() == "超高质量":
            resolution = 150  # 超高分辨率
        else:
            resolution = 100  # 高分辨率

        self.cup_vertices, self.cup_faces, self.cup_uv = generator.generate_high_res_cup(resolution)

        # 初始化顶点颜色（白色）
        self.vertex_colors = np.ones((len(self.cup_vertices), 3))

        self.status_var.set(f"杯子模型生成完成 - {len(self.cup_vertices)}个顶点，{len(self.cup_faces)}个面片")

    def setup_3d_display(self):
        """设置3D显示"""
        # 创建matplotlib图形
        self.fig = Figure(figsize=(8, 6), dpi=100)
        self.ax = self.fig.add_subplot(111, projection='3d')

        # 创建画布
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.root.children['!frame'].children['!labelframe2'])
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # 绑定鼠标事件
        self.canvas.mpl_connect('button_press_event', self.on_mouse_press)
        self.canvas.mpl_connect('motion_notify_event', self.on_mouse_move)
        self.canvas.mpl_connect('button_release_event', self.on_mouse_release)

        self.mouse_pressed = False
        self.last_mouse_pos = None

        # 初始显示
        self.update_3d_display()

    def load_pattern(self):
        """加载图案文件"""
        file_path = filedialog.askopenfilename(
            title="选择图案文件",
            filetypes=[
                ("图像文件", "*.png *.jpg *.jpeg *.bmp *.gif *.tiff"),
                ("PNG文件", "*.png"),
                ("JPEG文件", "*.jpg *.jpeg"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            try:
                self.pattern_image = Image.open(file_path)
                # 转换为RGB模式
                if self.pattern_image.mode != 'RGB':
                    self.pattern_image = self.pattern_image.convert('RGB')

                # 更新文件标签
                filename = os.path.basename(file_path)
                if len(filename) > 25:
                    filename = filename[:22] + "..."
                self.file_label.config(text=f"✅ {filename}", foreground='green')

                self.status_var.set(f"🟢 已加载图案: {os.path.basename(file_path)}")

                # 自动应用贴图
                self.apply_texture()

            except Exception as e:
                self.file_label.config(text="❌ 加载失败", foreground='red')
                messagebox.showerror("错误", f"无法加载图案文件:\n{str(e)}")

    def on_texture_change(self, value=None):
        """贴图参数改变时的回调"""
        if self.pattern_image is not None:
            self.apply_texture()

    def apply_texture(self):
        """应用贴图到杯子模型"""
        if self.pattern_image is None:
            messagebox.showwarning("警告", "请先加载图案文件")
            return

        self.status_var.set("🔄 正在应用贴图...")
        self.root.update()

        try:
            # 获取当前参数
            u_offset = self.u_var.get()
            v_offset = self.v_var.get()
            scale = self.scale_var.get()
            rotation = self.rot_var.get()

            # 应用高质量贴图算法
            self.vertex_colors = self.apply_high_quality_texture(
                u_offset, v_offset, scale, rotation
            )

            # 更新3D显示
            self.update_3d_display()

            self.status_var.set(f"✅ 贴图已更新 - 位置({u_offset:.2f}, {v_offset:.2f}) 大小{scale:.2f} 角度{rotation:.0f}°")

        except Exception as e:
            messagebox.showerror("错误", f"贴图应用失败:\n{str(e)}")
            self.status_var.set("❌ 贴图应用失败")

    def apply_high_quality_texture(self, u_offset, v_offset, scale, rotation):
        """高质量贴图算法 - 单张图片贴到特定位置，支持双线性插值"""
        pattern_array = np.array(self.pattern_image)
        pattern_h, pattern_w = pattern_array.shape[:2]

        # 初始化颜色数组（默认浅灰色，更好的视觉效果）
        base_color = np.array([0.95, 0.95, 0.95])  # 浅灰色
        colors = np.tile(base_color, (len(self.cup_vertices), 1))

        # 旋转矩阵
        angle_rad = np.radians(rotation)
        cos_a, sin_a = np.cos(angle_rad), np.sin(angle_rad)

        # 图片在UV空间中的尺寸（根据缩放调整）
        texture_width = scale
        texture_height = scale

        # 图片中心位置
        center_u = u_offset
        center_v = v_offset

        for i, (u, v) in enumerate(self.cup_uv):
            # 计算相对于图片中心的坐标
            rel_u = u - center_u
            rel_v = v - center_v

            # 应用旋转（以图片中心为旋转点）
            rotated_u = rel_u * cos_a - rel_v * sin_a
            rotated_v = rel_u * sin_a + rel_v * cos_a

            # 检查是否在图片范围内
            if (abs(rotated_u) <= texture_width / 2 and
                abs(rotated_v) <= texture_height / 2):

                # 将坐标映射到图片的0-1范围
                img_u = (rotated_u / texture_width) + 0.5
                img_v = (rotated_v / texture_height) + 0.5

                # 确保在有效范围内
                if 0 <= img_u <= 1 and 0 <= img_v <= 1:
                    # 使用双线性插值提升图片质量
                    pixel_color = self.bilinear_interpolation(
                        pattern_array, img_u, img_v, pattern_w, pattern_h
                    )
                    colors[i] = pixel_color / 255.0

        return colors

    def bilinear_interpolation(self, image, u, v, width, height):
        """双线性插值，提升图片渲染质量"""
        # 计算浮点坐标
        x = u * (width - 1)
        y = (1 - v) * (height - 1)  # 翻转Y轴

        # 获取四个邻近像素的整数坐标
        x1 = int(np.floor(x))
        x2 = min(x1 + 1, width - 1)
        y1 = int(np.floor(y))
        y2 = min(y1 + 1, height - 1)

        # 计算权重
        wx = x - x1
        wy = y - y1

        # 获取四个邻近像素的颜色
        c11 = image[y1, x1]
        c12 = image[y2, x1]
        c21 = image[y1, x2]
        c22 = image[y2, x2]

        # 双线性插值
        c1 = c11 * (1 - wx) + c21 * wx
        c2 = c12 * (1 - wx) + c22 * wx
        color = c1 * (1 - wy) + c2 * wy

        return color

    def update_3d_display(self):
        """更新3D显示 - 优化美观性和性能"""
        self.ax.clear()

        if self.cup_vertices is None:
            return

        # 创建3D多边形集合
        triangles = []
        colors_for_faces = []

        for face in self.cup_faces:
            triangle = self.cup_vertices[face]
            triangles.append(triangle)

            # 使用面片顶点的平均颜色
            face_color = np.mean(self.vertex_colors[face], axis=0)
            colors_for_faces.append(face_color)

        # 创建3D多边形集合，优化渲染效果
        poly_collection = Poly3DCollection(triangles, alpha=0.95, linewidths=0.05)
        poly_collection.set_facecolors(colors_for_faces)
        poly_collection.set_edgecolors('none')  # 移除边线，更美观

        self.ax.add_collection3d(poly_collection)

        # 设置坐标轴范围
        self.ax.set_xlim([-5, 5])
        self.ax.set_ylim([-5, 5])
        self.ax.set_zlim([0, 12])

        # 隐藏坐标轴，提升美观性
        self.ax.set_xticks([])
        self.ax.set_yticks([])
        self.ax.set_zticks([])
        self.ax.set_xlabel('')
        self.ax.set_ylabel('')
        self.ax.set_zlabel('')

        # 隐藏坐标轴线和背景
        self.ax.xaxis.pane.fill = False
        self.ax.yaxis.pane.fill = False
        self.ax.zaxis.pane.fill = False
        self.ax.xaxis.pane.set_edgecolor('none')
        self.ax.yaxis.pane.set_edgecolor('none')
        self.ax.zaxis.pane.set_edgecolor('none')

        # 设置背景色为白色
        self.ax.xaxis.pane.set_facecolor('white')
        self.ax.yaxis.pane.set_facecolor('white')
        self.ax.zaxis.pane.set_facecolor('white')

        # 设置视角
        self.ax.view_init(elev=15, azim=45)

        # 设置图形边距，使杯子居中显示
        self.ax.margins(0.1)

        # 刷新画布
        self.canvas.draw_idle()  # 使用draw_idle提升性能

    def on_mouse_press(self, event):
        """鼠标按下事件"""
        if event.inaxes == self.ax:
            self.mouse_pressed = True
            self.last_mouse_pos = (event.x, event.y)

    def on_mouse_move(self, event):
        """鼠标移动事件"""
        if self.mouse_pressed and event.inaxes == self.ax and self.last_mouse_pos:
            dx = event.x - self.last_mouse_pos[0]
            dy = event.y - self.last_mouse_pos[1]

            # 获取当前视角
            elev = self.ax.elev
            azim = self.ax.azim

            # 更新视角
            new_azim = azim + dx * 0.5
            new_elev = max(-90, min(90, elev - dy * 0.5))

            self.ax.view_init(elev=new_elev, azim=new_azim)
            self.canvas.draw()

            self.last_mouse_pos = (event.x, event.y)

    def on_mouse_release(self, event):
        """鼠标释放事件"""
        self.mouse_pressed = False
        self.last_mouse_pos = None

    def reset_texture(self):
        """重置贴图位置"""
        self.u_var.set(0.5)  # 图片中心位置
        self.v_var.set(0.5)  # 图片中心位置
        self.scale_var.set(0.3)  # 适中的图片大小
        self.rot_var.set(0)

        if self.pattern_image is not None:
            self.apply_texture()

    def reset_view(self):
        """重置视角"""
        self.ax.view_init(elev=20, azim=45)
        self.canvas.draw()

    def run(self):
        """运行应用程序"""
        self.root.mainloop()


def main():
    """主函数"""
    print("3D文创图案贴图Demo - 最终版本")
    print("正在启动应用程序...")

    try:
        # 创建并运行应用
        app = FinalTextureMapper()
        app.run()

    except Exception as e:
        print(f"应用程序运行错误: {e}")
        messagebox.showerror("错误", f"应用程序运行失败:\n{str(e)}")


if __name__ == "__main__":
    main()