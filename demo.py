"""
3D文创图案贴图Demo - 最终版本
高质量杯子模型生成和贴图功能的完整程序
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import numpy as np
from PIL import Image, ImageTk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
from mpl_toolkits.mplot3d.art3d import Poly3DCollection

# 设置matplotlib支持中文
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False


class HighQualityCupGenerator:
    """高质量杯子模型生成器"""

    def __init__(self):
        self.cup_height = 10.0
        self.cup_radius_bottom = 3.0
        self.cup_radius_top = 3.5
        self.cup_thickness = 0.2

    def generate_high_res_cup(self, resolution=120):
        """生成高分辨率杯子模型"""
        # 大幅提高分辨率：从50提升到120
        theta = np.linspace(0, 2*np.pi, resolution)
        heights = np.linspace(0, self.cup_height, resolution//2)

        vertices = []
        faces = []
        uv_coords = []

        # 生成杯身外表面（高密度网格）
        for i, h in enumerate(heights):
            r = self.cup_radius_bottom + (self.cup_radius_top - self.cup_radius_bottom) * (h / self.cup_height)
            for j, t in enumerate(theta[:-1]):
                x = r * np.cos(t)
                y = r * np.sin(t)
                z = h
                vertices.append([x, y, z])

                # 高精度UV坐标映射
                u = t / (2 * np.pi)
                v = h / self.cup_height
                uv_coords.append([u, v])

        # 生成面片（高密度）
        n_theta = len(theta) - 1
        n_height = len(heights)

        for i in range(n_height - 1):
            for j in range(n_theta):
                v1 = i * n_theta + j
                v2 = i * n_theta + (j + 1) % n_theta
                v3 = (i + 1) * n_theta + j
                v4 = (i + 1) * n_theta + (j + 1) % n_theta

                faces.append([v1, v2, v4])
                faces.append([v1, v4, v3])

        return np.array(vertices), np.array(faces), np.array(uv_coords)


class FinalTextureMapper:
    """最终版本贴图器 - 整合所有优化功能"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("3D文创图案贴图工具 - 最终版本")
        self.root.geometry("1200x800")

        # 数据存储
        self.pattern_image = None
        self.cup_vertices = None
        self.cup_faces = None
        self.cup_uv = None
        self.vertex_colors = None

        # 贴图参数
        self.current_texture_pos = [0.0, 0.0]
        self.texture_scale = 0.5
        self.texture_rotation = 0

        # 创建界面
        self.setup_ui()

        # 生成高质量杯子模型
        self.generate_cup_model()

        # 初始化3D显示
        self.setup_3d_display()

    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 左侧控制面板
        control_frame = ttk.LabelFrame(main_frame, text="控制面板", padding=10)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))

        # 图案加载
        ttk.Label(control_frame, text="图案文件:").pack(anchor=tk.W)
        ttk.Button(control_frame, text="加载图案",
                  command=self.load_pattern).pack(fill=tk.X, pady=2)

        # 位置控制
        ttk.Separator(control_frame, orient='horizontal').pack(fill=tk.X, pady=10)
        ttk.Label(control_frame, text="图片位置控制:").pack(anchor=tk.W)

        # U坐标
        ttk.Label(control_frame, text="图片中心 - 水平位置:").pack(anchor=tk.W)
        self.u_var = tk.DoubleVar(value=0.5)
        self.u_scale = ttk.Scale(control_frame, from_=0.0, to=1.0,
                                variable=self.u_var, orient=tk.HORIZONTAL,
                                command=self.on_texture_change)
        self.u_scale.pack(fill=tk.X, pady=2)

        # V坐标
        ttk.Label(control_frame, text="图片中心 - 垂直位置:").pack(anchor=tk.W)
        self.v_var = tk.DoubleVar(value=0.5)
        self.v_scale = ttk.Scale(control_frame, from_=0.0, to=1.0,
                                variable=self.v_var, orient=tk.HORIZONTAL,
                                command=self.on_texture_change)
        self.v_scale.pack(fill=tk.X, pady=2)

        # 缩放控制
        ttk.Label(control_frame, text="图片大小:").pack(anchor=tk.W)
        self.scale_var = tk.DoubleVar(value=0.3)
        self.scale_scale = ttk.Scale(control_frame, from_=0.05, to=1.0,
                                    variable=self.scale_var, orient=tk.HORIZONTAL,
                                    command=self.on_texture_change)
        self.scale_scale.pack(fill=tk.X, pady=2)

        # 旋转控制
        ttk.Label(control_frame, text="图片旋转角度 (以图片中心为轴):").pack(anchor=tk.W)
        self.rot_var = tk.DoubleVar(value=0)
        self.rot_scale = ttk.Scale(control_frame, from_=0, to=360,
                                  variable=self.rot_var, orient=tk.HORIZONTAL,
                                  command=self.on_texture_change)
        self.rot_scale.pack(fill=tk.X, pady=2)

        # 质量选项
        ttk.Separator(control_frame, orient='horizontal').pack(fill=tk.X, pady=10)
        ttk.Label(control_frame, text="渲染质量:").pack(anchor=tk.W)
        self.quality_var = tk.StringVar(value="超高质量")
        quality_frame = ttk.Frame(control_frame)
        quality_frame.pack(fill=tk.X, pady=2)
        ttk.Radiobutton(quality_frame, text="高质量", variable=self.quality_var,
                       value="高质量").pack(side=tk.LEFT)
        ttk.Radiobutton(quality_frame, text="超高质量", variable=self.quality_var,
                       value="超高质量").pack(side=tk.LEFT)

        # 操作按钮
        ttk.Separator(control_frame, orient='horizontal').pack(fill=tk.X, pady=10)
        ttk.Button(control_frame, text="应用贴图",
                  command=self.apply_texture).pack(fill=tk.X, pady=2)
        ttk.Button(control_frame, text="重置位置",
                  command=self.reset_texture).pack(fill=tk.X, pady=2)
        ttk.Button(control_frame, text="重置视角",
                  command=self.reset_view).pack(fill=tk.X, pady=2)

        # 右侧3D视图区域
        view_frame = ttk.LabelFrame(main_frame, text="3D实时预览", padding=10)
        view_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        return view_frame

    def generate_cup_model(self):
        """生成高质量杯子模型"""
        self.status_var.set("正在生成高质量杯子模型...")
        self.root.update()

        # 使用高分辨率生成器
        generator = HighQualityCupGenerator()

        # 根据质量设置选择分辨率
        if self.quality_var.get() == "超高质量":
            resolution = 150  # 超高分辨率
        else:
            resolution = 100  # 高分辨率

        self.cup_vertices, self.cup_faces, self.cup_uv = generator.generate_high_res_cup(resolution)

        # 初始化顶点颜色（白色）
        self.vertex_colors = np.ones((len(self.cup_vertices), 3))

        self.status_var.set(f"杯子模型生成完成 - {len(self.cup_vertices)}个顶点，{len(self.cup_faces)}个面片")

    def setup_3d_display(self):
        """设置3D显示"""
        # 创建matplotlib图形
        self.fig = Figure(figsize=(8, 6), dpi=100)
        self.ax = self.fig.add_subplot(111, projection='3d')

        # 创建画布
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.root.children['!frame'].children['!labelframe2'])
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # 绑定鼠标事件
        self.canvas.mpl_connect('button_press_event', self.on_mouse_press)
        self.canvas.mpl_connect('motion_notify_event', self.on_mouse_move)
        self.canvas.mpl_connect('button_release_event', self.on_mouse_release)

        self.mouse_pressed = False
        self.last_mouse_pos = None

        # 初始显示
        self.update_3d_display()

    def load_pattern(self):
        """加载图案文件"""
        file_path = filedialog.askopenfilename(
            title="选择图案文件",
            filetypes=[
                ("图像文件", "*.png *.jpg *.jpeg *.bmp *.gif *.tiff"),
                ("PNG文件", "*.png"),
                ("JPEG文件", "*.jpg *.jpeg"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            try:
                self.pattern_image = Image.open(file_path)
                # 转换为RGB模式
                if self.pattern_image.mode != 'RGB':
                    self.pattern_image = self.pattern_image.convert('RGB')

                self.status_var.set(f"已加载图案: {os.path.basename(file_path)}")

                # 自动应用贴图
                self.apply_texture()

            except Exception as e:
                messagebox.showerror("错误", f"无法加载图案文件:\n{str(e)}")

    def on_texture_change(self, value=None):
        """贴图参数改变时的回调"""
        if self.pattern_image is not None:
            self.apply_texture()

    def apply_texture(self):
        """应用贴图到杯子模型"""
        if self.pattern_image is None:
            messagebox.showwarning("警告", "请先加载图案文件")
            return

        self.status_var.set("正在应用贴图...")
        self.root.update()

        try:
            # 获取当前参数
            u_offset = self.u_var.get()
            v_offset = self.v_var.get()
            scale = self.scale_var.get()
            rotation = self.rot_var.get()

            # 应用高质量贴图算法
            self.vertex_colors = self.apply_high_quality_texture(
                u_offset, v_offset, scale, rotation
            )

            # 更新3D显示
            self.update_3d_display()

            self.status_var.set("贴图应用完成")

        except Exception as e:
            messagebox.showerror("错误", f"贴图应用失败:\n{str(e)}")
            self.status_var.set("贴图应用失败")

    def apply_high_quality_texture(self, u_offset, v_offset, scale, rotation):
        """高质量贴图算法 - 单张图片贴到特定位置"""
        pattern_array = np.array(self.pattern_image)
        pattern_h, pattern_w = pattern_array.shape[:2]

        # 初始化颜色数组（默认白色）
        colors = np.ones((len(self.cup_vertices), 3))

        # 旋转矩阵
        angle_rad = np.radians(rotation)
        cos_a, sin_a = np.cos(angle_rad), np.sin(angle_rad)

        # 图片在UV空间中的尺寸（根据缩放调整）
        texture_width = scale
        texture_height = scale

        # 图片中心位置
        center_u = u_offset
        center_v = v_offset

        for i, (u, v) in enumerate(self.cup_uv):
            # 计算相对于图片中心的坐标
            rel_u = u - center_u
            rel_v = v - center_v

            # 应用旋转（以图片中心为旋转点）
            rotated_u = rel_u * cos_a - rel_v * sin_a
            rotated_v = rel_u * sin_a + rel_v * cos_a

            # 检查是否在图片范围内
            if (abs(rotated_u) <= texture_width / 2 and
                abs(rotated_v) <= texture_height / 2):

                # 将坐标映射到图片的0-1范围
                img_u = (rotated_u / texture_width) + 0.5
                img_v = (rotated_v / texture_height) + 0.5

                # 确保在有效范围内
                if 0 <= img_u <= 1 and 0 <= img_v <= 1:
                    # 映射到图像像素坐标
                    img_x = int(img_u * (pattern_w - 1))
                    img_y = int((1 - img_v) * (pattern_h - 1))  # 翻转Y轴

                    # 边界检查
                    img_x = max(0, min(pattern_w - 1, img_x))
                    img_y = max(0, min(pattern_h - 1, img_y))

                    # 获取像素颜色
                    pixel_color = pattern_array[img_y, img_x]
                    colors[i] = pixel_color / 255.0

            # 如果不在图片范围内，保持默认白色

        return colors

    def update_3d_display(self):
        """更新3D显示"""
        self.ax.clear()

        if self.cup_vertices is None:
            return

        # 创建3D多边形集合
        triangles = []
        colors_for_faces = []

        for face in self.cup_faces:
            triangle = self.cup_vertices[face]
            triangles.append(triangle)

            # 使用面片顶点的平均颜色
            face_color = np.mean(self.vertex_colors[face], axis=0)
            colors_for_faces.append(face_color)

        # 创建3D多边形集合
        poly_collection = Poly3DCollection(triangles, alpha=0.8, linewidths=0.1)
        poly_collection.set_facecolors(colors_for_faces)
        poly_collection.set_edgecolors('lightgray')

        self.ax.add_collection3d(poly_collection)

        # 设置坐标轴范围
        self.ax.set_xlim([-4, 4])
        self.ax.set_ylim([-4, 4])
        self.ax.set_zlim([0, 12])

        # 设置标签
        self.ax.set_xlabel('X')
        self.ax.set_ylabel('Y')
        self.ax.set_zlabel('Z')

        # 设置标题
        self.ax.set_title('3D杯子贴图预览', fontsize=12, fontweight='bold')

        # 设置视角
        self.ax.view_init(elev=20, azim=45)

        # 刷新画布
        self.canvas.draw()

    def on_mouse_press(self, event):
        """鼠标按下事件"""
        if event.inaxes == self.ax:
            self.mouse_pressed = True
            self.last_mouse_pos = (event.x, event.y)

    def on_mouse_move(self, event):
        """鼠标移动事件"""
        if self.mouse_pressed and event.inaxes == self.ax and self.last_mouse_pos:
            dx = event.x - self.last_mouse_pos[0]
            dy = event.y - self.last_mouse_pos[1]

            # 获取当前视角
            elev = self.ax.elev
            azim = self.ax.azim

            # 更新视角
            new_azim = azim + dx * 0.5
            new_elev = max(-90, min(90, elev - dy * 0.5))

            self.ax.view_init(elev=new_elev, azim=new_azim)
            self.canvas.draw()

            self.last_mouse_pos = (event.x, event.y)

    def on_mouse_release(self, event):
        """鼠标释放事件"""
        self.mouse_pressed = False
        self.last_mouse_pos = None

    def reset_texture(self):
        """重置贴图位置"""
        self.u_var.set(0.5)  # 图片中心位置
        self.v_var.set(0.5)  # 图片中心位置
        self.scale_var.set(0.3)  # 适中的图片大小
        self.rot_var.set(0)

        if self.pattern_image is not None:
            self.apply_texture()

    def reset_view(self):
        """重置视角"""
        self.ax.view_init(elev=20, azim=45)
        self.canvas.draw()

    def run(self):
        """运行应用程序"""
        self.root.mainloop()


def main():
    """主函数"""
    print("3D文创图案贴图Demo - 最终版本")
    print("正在启动应用程序...")

    try:
        # 创建并运行应用
        app = FinalTextureMapper()
        app.run()

    except Exception as e:
        print(f"应用程序运行错误: {e}")
        messagebox.showerror("错误", f"应用程序运行失败:\n{str(e)}")


if __name__ == "__main__":
    main()